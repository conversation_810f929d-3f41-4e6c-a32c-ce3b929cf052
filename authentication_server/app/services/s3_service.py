import boto3
import logging
from botocore.exceptions import Client<PERSON>rror
from fastapi import HTTPException
import io
from ..config import AWS_REGION

logger = logging.getLogger(__name__)

class S3Service:
    def __init__(self):
        # Using the IAM role assigned to the EC2 instance via IMDSv2
        self.s3_client = boto3.client('s3', region_name=AWS_REGION)

    def download_file(self, bucket_name: str, file_path: str) -> bytes:
        """
        Download a file from S3
        """
        logger.info(f"S3Service download_file: bucket_name={repr(bucket_name)}, file_path={repr(file_path)}") # Added for debugging
        try:
            response = self.s3_client.get_object(Bucket=bucket_name, Key=file_path)
            return response['Body'].read()
        except ClientError as e:
            error_code = e.response.get('Error', {}).get('Code', 'Unknown')
            error_message = e.response.get('Error', {}).get('Message', str(e))
            
            logger.error(f"Error downloading file from S3: {error_code} - {error_message}")
            
            if error_code == 'NoSuchKey':
                raise HTTPException(status_code=404, detail=f"File not found: {file_path}")
            elif error_code == 'AccessDenied':
                raise HTTPException(status_code=403, detail=f"Access denied to S3 bucket: {bucket_name}")
            elif error_code == 'NoSuchBucket':
                raise HTTPException(status_code=404, detail=f"Bucket not found: {bucket_name}")
            else:
                raise HTTPException(status_code=500, detail=f"S3 error: {error_message}")

    def upload_file(self, bucket_name: str, file_path: str, file_content: bytes) -> dict:
        """
        Upload a file to S3
        """
        try:
            self.s3_client.upload_fileobj(
                io.BytesIO(file_content),
                Bucket=bucket_name,
                Key=file_path
            )
            return {"success": True, "bucket": bucket_name, "file_path": file_path}
        except ClientError as e:
            error_code = e.response.get('Error', {}).get('Code', 'Unknown')
            error_message = e.response.get('Error', {}).get('Message', str(e))
            
            logger.error(f"Error uploading file to S3: {error_code} - {error_message}")
            
            if error_code == 'AccessDenied':
                raise HTTPException(status_code=403, detail=f"Access denied to S3 bucket: {bucket_name}")
            elif error_code == 'NoSuchBucket':
                raise HTTPException(status_code=404, detail=f"Bucket not found: {bucket_name}")
            else:
                raise HTTPException(status_code=500, detail=f"S3 error: {error_message}")

    def list_files(self, bucket_name: str, prefix: str = "") -> list:
        """
        List files in S3 bucket with optional prefix
        """
        try:
            response = self.s3_client.list_objects_v2(
                Bucket=bucket_name,
                Prefix=prefix
            )
            
            files = []
            if 'Contents' in response:
                for obj in response['Contents']:
                    files.append({
                        'key': obj['Key'],
                        'size': obj['Size'],
                        'last_modified': obj['LastModified'].isoformat()
                    })
            
            return files
        except ClientError as e:
            error_code = e.response.get('Error', {}).get('Code', 'Unknown')
            error_message = e.response.get('Error', {}).get('Message', str(e))
            
            logger.error(f"Error listing files in S3: {error_code} - {error_message}")
            
            if error_code == 'AccessDenied':
                raise HTTPException(status_code=403, detail=f"Access denied to S3 bucket: {bucket_name}")
            elif error_code == 'NoSuchBucket':
                raise HTTPException(status_code=404, detail=f"Bucket not found: {bucket_name}")
            else:
                raise HTTPException(status_code=500, detail=f"S3 error: {error_message}")

    def delete_file(self, bucket_name: str, file_path: str) -> dict:
        """
        Delete a file from S3
        """
        try:
            self.s3_client.delete_object(Bucket=bucket_name, Key=file_path)
            return {"success": True, "bucket": bucket_name, "file_path": file_path}
        except ClientError as e:
            error_code = e.response.get('Error', {}).get('Code', 'Unknown')
            error_message = e.response.get('Error', {}).get('Message', str(e))
            
            logger.error(f"Error deleting file from S3: {error_code} - {error_message}")
            
            if error_code == 'AccessDenied':
                raise HTTPException(status_code=403, detail=f"Access denied to S3 bucket: {bucket_name}")
            elif error_code == 'NoSuchBucket':
                raise HTTPException(status_code=404, detail=f"Bucket not found: {bucket_name}")
            else:
                raise HTTPException(status_code=500, detail=f"S3 error: {error_message}") 