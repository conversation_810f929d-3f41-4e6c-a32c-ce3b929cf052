from fastapi import APIRouter, Depends, UploadFile, File, HTTPException, Header, Query, Path
from fastapi.responses import Response, JSONResponse
from typing import List, Optional, Dict # Added Dict
from ..services.s3_service import S3Service
from ..services.auth_service import get_current_client_flexible_auth # Changed import
import logging
router_logger = logging.getLogger(__name__)

from pydantic import BaseModel, constr
import re


router = APIRouter(prefix="/s3", tags=["S3 Operations"])

# Model for list files response
class S3FileInfo(BaseModel):
    key: str
    size: int
    last_modified: str

class S3ListResponse(BaseModel):
    bucket: str
    files: List[S3FileInfo]
    


# Bucket name validation - only allow alphanumeric, dots, and hyphens
# Regex pattern for bucket name validation
BUCKET_NAME_PATTERN = r'^[a-z0-9][a-z0-9\.\-]{1,61}[a-z0-9]$'

@router.get("/buckets/{bucket_name}/files", response_model=S3ListResponse)
async def list_files(
    bucket_name: str = Path(..., description="S3 bucket name"),
    prefix: Optional[str] = Query("", description="Optional prefix filter"),
    current_client: Dict = Depends(get_current_client_flexible_auth)
):
    """
    List files in the specified S3 bucket
    """
    # Validate bucket name against regex pattern
    if not re.match(BUCKET_NAME_PATTERN, bucket_name):
        raise HTTPException(status_code=400, detail="Invalid bucket name format")
    
    s3_service = S3Service()
    files = s3_service.list_files(bucket_name=bucket_name, prefix=prefix)
    return {"bucket": bucket_name, "files": files}

@router.get("/buckets/{bucket_name}/file") # Path changed
async def download_file(
    bucket_name: str = Path(..., description="S3 bucket name"),
    object_key: str = Query(..., description="S3 object key"), # Changed from file_path to object_key query param
    current_client: Dict = Depends(get_current_client_flexible_auth)
):
    """
    Download a file from the specified S3 bucket
    """
    # Validate bucket name against regex pattern
    if not re.match(BUCKET_NAME_PATTERN, bucket_name):
        raise HTTPException(status_code=400, detail="Invalid bucket name format")
    
    s3_service = S3Service()
    router_logger.info(f"Router download_file: bucket_name={repr(bucket_name)}, object_key={repr(object_key)}")
    file_content = s3_service.download_file(bucket_name=bucket_name, file_path=object_key)
    
    # Determine content type (basic implementation)
    content_type = "application/octet-stream"
    if object_key.endswith(".json"):
        content_type = "application/json"
    elif object_key.endswith(".txt"):
        content_type = "text/plain"
    elif object_key.endswith(".html"):
        content_type = "text/html"
    elif object_key.endswith(".pdf"):
        content_type = "application/pdf"
    elif object_key.endswith(".jpg") or object_key.endswith(".jpeg"):
        content_type = "image/jpeg"
    elif object_key.endswith(".png"):
        content_type = "image/png"
    
    return Response(content=file_content, media_type=content_type)

@router.post("/buckets/{bucket_name}/file") # Path changed
async def upload_file(
    bucket_name: str = Path(..., description="S3 bucket name"),
    object_key: str = Query(..., description="S3 object key to save as"), # Changed from file_path to object_key query param
    file: UploadFile = File(...),
    current_client: Dict = Depends(get_current_client_flexible_auth)
):
    """
    Upload a file to the specified S3 bucket
    """
    # Validate bucket name against regex pattern
    if not re.match(BUCKET_NAME_PATTERN, bucket_name):
        raise HTTPException(status_code=400, detail="Invalid bucket name format")
    
    s3_service = S3Service()
    file_content = await file.read()
    result = s3_service.upload_file(bucket_name=bucket_name, file_path=object_key, file_content=file_content)
    return JSONResponse(content=result)

@router.delete("/buckets/{bucket_name}/file") # Path changed
async def delete_file(
    bucket_name: str = Path(..., description="S3 bucket name"),
    object_key: str = Query(..., description="S3 object key to delete"), # Changed from file_path to object_key query param
    current_client: Dict = Depends(get_current_client_flexible_auth)
):
    """
    Delete a file from the specified S3 bucket
    """
    # Validate bucket name against regex pattern
    if not re.match(BUCKET_NAME_PATTERN, bucket_name):
        raise HTTPException(status_code=400, detail="Invalid bucket name format")
    
    s3_service = S3Service()
    result = s3_service.delete_file(bucket_name=bucket_name, file_path=object_key)
    return JSONResponse(content=result) 